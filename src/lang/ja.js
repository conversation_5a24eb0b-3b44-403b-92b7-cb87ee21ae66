
const components = {
    authorize: {
        name: '授权',
        authorizeFile: '文件授权',
        authorizeTip: '您好，使用该功能需要授权上上签解析合同文件内容。',
        authorizeAgree: '《合同授权协议》',
    },
    countDown: {
        resendCode: '重新获取',
        getVerifyCode: '获取验证码',
    },
    guide: {
        name: '功能指引',
        description1: 'Hubble，一种全新的工作体验。',
        description2: '你好，我是哈勃，你的签约智能助手',
        description3: '一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。',
        description4: '我们准备了必要的使用介绍来帮助快速上手，',
        description5: '可以通过下方按钮轻松探索特色功能。',
        startHubble: '即刻开启 Hubble 之旅',
        newFileChat: '新建文档对话',
        inputContent: '输入对话内容',
        underlineQuote: '划词引用',
        outputLanguage: '输出内容语言',
        suggestQuestions: '建议问题',
    },
    header: {
        accountConfig: '账号设置',
        share: '分享',
        myVersion: '我的版本',
        shareHubble: '分享 Hubble 智能文档',
        inviteToHubble: {
            1: '邀请您参与Hubble智能文档',
            2: '合同名称：',
            3: '点击链接直接进入：',
            4: '查看密码：',
            5: '复制该信息，分享给其他人即可',
        },
        openClose: '启/关闭分享链接，将决定其他人是否可以继续查看文档',
        inviteFriend: '输入好友手机号，邀请体验：',
        inputFriendNumber: '请输入好友手机号',
        sendInvite: '发送邀请',

        packageDetail: '套餐详情',
        packageVersion: '套餐版本：',
        currentPackage: '当前套餐：',
        validDuration: '（生效及截止日期）',
        hasUsedNum: '已用{num}份',
        hasUsedTimes: '已用{time}次',
        totalNums: '共{num}份文档',
        totalTimes: '共{times}次对话',
        buyedPackage: '已购套餐：',
        buyedContent: '（{num}期，套餐容量同上）',

    },
    helperFloat: {
        advice: '咨询建议',
        hide: '隐藏',
        suggest: '提建议',
        onlineService: '在线客服',
        minimize: '最小化',
        enlarge: '放大',
        zoomOut: '缩小',
        person: '个人',
        ent: '企业',
        checkMoreVersion: '查看更多版本',
        submitSuccess: '上报成功',
        reportLog: '上报日志',
    },
    package: {
        myVersion: '我的版本',
        tryVersion: '体验版',
        free: 'Free / 免费',
        packageDetail: '套餐包含内容为：',
        numFiles: '份文档',
        timesChat: '次对话',
        pagePerFile: '300页/文档',
        sizePerFile: '20MB/文档',
        hasOpen: '已开通',
        validTo: '有效期至',
        openNow: '立即开通',
        personVersion: '个人版',
        costPerMonth: '${price} / 月',
        continueBuy: '续充套餐',
        groupVersion: '团队版',
        connectUs: '联系我们',
        connectTip: '可以联系我们的专业人员，我们会根据您的情况提供专属方案。',
        hasRead: '我已阅读并同意',
        agreenName: '《文档授权协议》',
        readAgreeTip: '请先阅读并同意《文档授权协议》',
    },
    quickPay: {
        pleaseScanPay: '请扫码支付',
        openFun: '开通功能：',
        payAmount: '支付金额：',
        unit: '元',
        payTip: '1.请扫码完成支付，完成支付后可在个人中心-我的订单中申请发票。',
        aliPay: '支付宝支付',
        weixinPay: '微信支付',
    },
};
const lang = {
    common: {
        tip: '提示',
        confirm: '确定',
        confirmWS: '确 认',
        cancel: '取消',
        exit: '退出',
        send: '发送',
        rename: '重命名',
        delete: '删除',
        copyInfo: '复制信息',
        newName: '新名称',
    },
    staticOpt: {
        goHome: '返回首页',
        downloadApp: '下载上上签APP',
    },
    tips: {
        noData: '没有数据',
        deleteSuccess: '删除成功',
        copySuccess: '复制成功',
        openSuccess: '开通成功',
        renameSuccess: '重命名成功',
        packageNotEnough: '套餐余额不足',
        uploadFailed: '上传失败',
        noMore: '没有更多了',

        deleteChatTip: '此操作将永久删除该话题, 是否继续?',
        pleaseOpenVersion: '请先开通需要的版本',
        pdfSupportOnly: '目前仅支持PDF文档',
        inputPhone: '请输入手机号',
        inputVerifyCode: '请输入验证码',
        lackAccount: '请填写账号后再获取',
        pleaseCheckAuto: '请点击按钮进行智能验证',
        notAccessPage: '无法访问此页面',
        page404: '没有找到您访问的页面（404）',
        systemOcupied: '系统繁忙，请稍后再试。',
    },
    login: {
        welcome: '欢迎使用Hubble',
        welcomeTip: '智能签约助手',
        name: '登录',
        loginOrRegister: '登录/注册',
        hasReadAndAgree: '我已阅读并同意',
        and: '和',
        bestsignAgreement: '上上签服务协议',
        digitalCertificateAgreement: '数字证书使用协议',
        privacyPolicy: '隐私政策',
        autoRegisterForNewPhone: '未注册手机号验证后会自动创建账号',
        sendSuc: '发送成功',
    },
    upload: {
        name: '上传文档',
        selectFile: '选择文档',
        dragToUpload: '将文档拖拽至此上传',
        pdfOnly: '目前仅支持PDF文档',
        releaseMouseToUpload: '释放鼠标完成上传',
    },
    share: {
        inviteToUse: '邀请您参与Hubble智能文档',
        fileName: '文档名称：',
        inputShareCodeToView: '填写分享码查看文档：',
        inputShareCodeTip: '请输入6位数字分享码',
    },
    chat: {
        switchOutputLanguage: '输出语言切换',
        chinese: '中文语言',
        english: '英文语言',
        selectAndClickAsk: '选中文字后点击提问',
        downloadFile: '文档下载',
        sourceFile: '源文件',
        sourceFileWithChat: '源文件 (携带对话)',
        fileAnalyzing: '文档解析中',
        continuousChat: '连续对话模式：',
        quoteContent: '引用内容：',
        suggestionsQuestions: '建议问题',
        inputQuestionTip: '请输入您的问题',

        selectToQuestTip: '在下方输入或在合同上选取后点击“Hubble”，进行提问',
        question: '问题：',
        explainTip: '~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记',
        relatedContent: '相关内容：',
        deleteConversation: '删除对话',
        openContinuousModel: '开启连续对话',
        confirmToDelete: '确认删除该对话吗？',
    },
    workspace: {
        create: '作成済み',
        reviewing: '審査中',
        completed: '完了',
        noData: 'データなし',
        introduce: '{keyword}の説明',
        termsDetail: '用語の詳細',
        extractFormat: '抽出形式',
        optional: '任意',
        required: '必須',
        operate: '操作',
        detail: '詳細',
        delete: '削除',
        agreement: {
            uploadError: 'アップロードできるのはPDF、DOC、またはDOCXファイルのみです。',
            extractionRequest: '抽出リクエストが送信されました。後で用語リストで結果を確認してください。',
            upload: 'ファイルのアップロード',
            define: '用語の定義',
            extract: '協定の抽出',
            drag: 'ファイルをここにドラッグするか、',
            add: 'クリックして追加',
            format: 'doc、docx、pdf形式をサポート',
            fileName: 'ファイル名',
            status: '状態',
            completed: 'アップロード完了',
            failed: 'アップロード失敗',
            size: 'サイズ',
            terms: '用語',
            success: 'ファイル抽出完了、合計{total}個',
            ongoing: 'ファイル抽出中... 合計{total}個',
            tips: 'この画面をスキップしても抽出結果に影響はありません',
            others: '続けてアップロード',
            result: '抽出結果のダウンロードページに移動',
            curProgress: '現在の進捗: ',
            refresh: '更新',
            details: '{successNum}個ロード済み、合計{length}個',
            start: '抽出開始',
            more: 'ファイルを追加',
            skip: '抽出をスキップし、アップロードを完了。',
            tiqu: '抽出開始',
            chouqu: '抽出開始',
        },
        review: {
            distribution: '配布審査',
            Incomplete: '未終了',
            createReview: '審査を作成',
            manageReview: '審査管理',
            reviewDetail: '審査の詳細',
            reviewId: '審査番号',
            reviewStatus: '審査状態',
            reviewName: '審査名',
            reviewStartTime: '審査開始時間',
            reviewCompleteTime: '審査終了時間',
            reviewDesc: 'バージョン：バージョン{reviewVersion}  |  審査番号：{reviewId}',
            distribute: '審査を開始',
            drag: '審査待ちの協定をこの領域にドラッグ',
            content: '審査待ちの内容',
            current: '配布待ち記録',
            history: '履歴記録',
            page: '第{page}ページ：',
            users: '審査が必要なユーザー',
            message: 'メッセージ',
            modify: '修正',
            placeholder: '複数のユーザーはセミコロン";"で区切ってください',
            submit: '確定',
            reupload: '審査の再アップロード',
            finish: '審査終了',
            reviewSummary: '審査概要',
            initiator: '審査の発起人',
            versionSummary: 'バージョン概要',
            version: 'バージョン',
            versionOrder: '第{version}版',
            curReviewStatus: '現在のバージョン審査状態',
            curReviewVersion: '現在のバージョン',
            curReviewPopulation: '現在のバージョン審査人数',
            curReviewStartTime: '現在のバージョン審査開始時間',
            curReviewInitiator: '現在のバージョン審査発起人',
            checkComments: '修正意見を集約表示',
            overview: '審査結果の概要',
            reviewer: '審査者',
            reviewResult: '審査結果',
            replyTime: '返信時間',
            agreement: '審査の協定',
            files: '関連協定',
            fileName: '協定名',
            numberOfModificationSuggestions: '修正意見数',
            uploadTime: 'アップロード時間',
            download: 'ダウンロード',
            dispatch: '配布',
            recent: '最新の審査時間：',
            replyContent: '審査の返信内容',
            advice: '協定の修正意見',
            noIdea: '修正意見なし',
            origin: '原文内容：',
            revised: '修正後の内容：',
            suggestion: '修正意見：',
            revisionFiles: '修正協定',
            staffReplyAggregation: '修正情報の集約',
            staffReply: '{name}の審査情報',
            tips: '提示',
            tipsContent: '終了後、この審査は配布及び後続操作をサポートしなくなります。続けますか',
            confirm: '確定',
            cancel: 'キャンセル',
            successMessage: '終了しました',
            PASS: '承認',
            NOT_PASS: '不承認',
            uploadErrorMessage: '現在、アップロードできるのはDOCX形式のファイルのみです。',
            successInitiated: '審査が開始されました',
        },
        contentTracing: {
            title: '内容追跡',
            fieldContent: 'フィールド内容',
            originalResult: 'オリジナル結果',
            contentSource: '内容の出典',
            page: 'ページ',
        },
    },
    pdf: {
        previewFail: 'File preview failed',
        pager: '{x}/{y}ページ目',
        parseFailed: 'Failed to parse the pdf file, please click "OK" to try again',
        confirm: 'Confirm',
    },
    contractCompare: {
        reUpload: '再アップロード',
        title: '契約書比較',
        packagePurchase: 'プラン購入',
        packagePurchaseTitle: '【{title}機能】プラン購入',
        myPackage: 'マイプラン',
        packageDetail: 'プラン詳細',
        per: '次',
        packageContent: 'プラン内容：',
        num: '{type}次数',
        limitTime: '有効期間',
        month: '月',
        payNow: '今すぐ購入',
        contactUs: 'お問い合わせ | QRコードをスキャンして専門アドバイザーに相談',
        compareInfo1: 'ご利用ガイド：',
        compareInfo2: '{index}、購入{type}に基づく利用可能限度額は、対応する企業の全メンバーが利用可能です。個人でご利用の場合は、画面上部のログイン主体を個人アカウントに切り替えてください。',
        compareInfo3: '{index}、アップロードした契約書の{per}数に基づく使用量計算',
        codePay: 'QRコードをスキャンして支払い',
        aliPay: 'アリペイ支払い',
        wxPay: 'ウィーチャット支払い',
        payIno: '機能有効化 | 購入対象 | 支払金額',
        finishPay: '支払い完了',
        paySuccess: '購入成功',
        originFile: '原本契約書ファイル',
        compareFile: '比較用契約書ファイル',
        documentSelect: 'ファイルを選択',
        comparisonResult: '比較結果',
        history: '履歴',
        currentHistory: '文書記録',
        noData: 'データなし',
        differences: '{num}つの差異',
        historyLog: '{num}件の記録',
        uploadLimit: '比較するファイルをドラッグ＆ドロップ | 対応形式: PDF（スキャン済み含む）、Word',
        dragInfo: 'マウスを離してアップロード',
        uploadError: '非対応ファイル形式',
        pageNum: '第{page}页',
        difference: '差異 {num}',
        download: '比較結果をダウンロード',
        comparing: '契約書比較中...',
        tip: '通知',
        confirm: '確定',
        toBuy: '購入へ進む',
        translate: '契約書翻訳',
        doCompare: '比較',
        doTranslate: '翻訳',
        review: '契約書審査',
        doReview: '審査',
        reviewUploadFile: '審査対象ファイルをここにドラッグ＆ドロップ',
        reviewUpload: '審査基準ファイルをドラッグ | 例：「販売代理店管理規程」「調達規程」| 対応形式: PDF、Word',
        reviewOriginFile: '審査対象契約書',
        reviewTargetFile: '審査基準',
        reviewResult: '審査結果',
        uploadReviewFile: '審査基準ファイルをアップロード',
        risk: 'リスク項目 {num}',
        risks: '{num}つのリスク項目',
        startReview: '審査を開始',
        reviewing: '契約書審査中...',
        noRisk: '審査完了 - リスク未検出',
        allowUpload: '審査の参考として、「調達管理規程」などの社内規程、コンプライアンス規定、部門ガイドラインをアップロード可能です。| 例: 「甲は契約締結後5日以内に支払いを完了すること」 | 如：甲方需在合同签订后的5日内完成付款。',
        notAllowUpload: '曖昧な表現や原則的な説明を審査基準に使用しないでください。 | 例: 「全ての契約条項は関連法令に準拠すること」',
        resumeReview: '次のファイルへ進む',
        close: '閉じる',
        extract: '契約書抽出',
        extractTitle: '抽出するキーワード',
        selectKeyword: '下記の「キーワード」から選択',
        keyword: 'キーワード',
        addKeyword: '追加{keyword}',
        introduce: '{keyword}定義',
        startExtract: '抽出を開始',
        extractTargetFile: '抽出対象契約書',
        extractKeyWord: 'キーワード抽出',
        extracting: '契約書抽出中...',
        extractResult: '抽出結果',
        extractUploadFile: '抽出するファイルをドラッグ＆ドロップ',
        needExtractKeyword: '抽出するキーワードを選択',
        summary: '契約書要約',
        keySummary: 'キーワード要約',
        deleteKeywordConfirm: 'このキーワードを削除しますか',
        keywordPosition: 'キーワードの位置',
        riskJudgement: 'リスク判定',
        judgeTargetContract: 'リスク判定を開始',
        interpretTargetContract: 'AI解読済み契約書',
        startJudge: 'リスク評価を開始する',
        startInterpret: '解読開始',
        uploadText: 'リスク判断が必要なファイルのアップロードをお願いします',
        interpretText: '解読が必要なファイルをアップロードしてください',
        startTips: 'これでリスクはんだんを始められです',
        interpretTips: 'これよりAI解読を開始いたします',
        infoExtract: '抽出情報',
    },
    judgeRisk: {
        title: 'AI弁護士',
        deepInference: 'AI弁護士（深度推論）',
        showAll: 'Show More',
        tips: 'Judging',
        dialogTitle: '「AI弁護士」による契約書の審査',
        aiInterpret: 'AI解読',
    },
    keyInfoExtract: {
        operate: '情報抽出',
        contractType: 'Predicted Contract Types',
        tooltips: 'Select the Key Information',
        predictText: 'Predicting',
        extractText: 'Extracting',
        errorMessage: 'Your usage limit has been used up, if you have further needs, you can fill out the questionaire, we will contact you and replenish more dosage for you.',
        result: 'result:',
    },
    agent: {
        extractTitle: '情報抽出',
        riskTitle: 'AI弁護士',
        feedback: '調査フィードバック',
        toMini: 'ミニアプリで確認',
        otherContract: '他の契約書の潜在リスクを分析?',
        others: 'その他',
        submit: '送信',
        autoExtract: '抽出完了まで自動処理継続',
        autoRisk: '分析プロセスを自動実行',
        aiGenerated: 'AI Generated - © BestSign',
        chooseRisk: '解析対象ファイルを選択',
        chooseExtract: '抽出用ソースファイル指定',
        analyzing: 'コンテンツ解析実行中',
        advice: '修正案自動作成中',
        options: '選択肢生成処理中',
        inputTips: '正確な情報を入力必須',
        chargeTip: '残高不足のためチャージ必須',
        original: '原本',
        revision: '改善提案',
        diff: '比較',
        locate: '原稿位置特定処理中',
        custom: 'カスタム審査ルールを入力してください',
        content: '原文の位置',
        satisfy: '对分析结果满意，继续下一项分析',
        dissatisfy: '对分析结果不满意，重新进行分析',
        selectFunc: 'ご希望の機能をお選びください',
        deepInference: 'AI弁護士（深度推論）',
        deepThinking: '深い思考中',
        deepThoughtCompleted: '深い思考が完了しました',
        reJudge: '再判断',
        confirm: '確認',
        tipsContent: '再判定を行うと利用回数が減ります。続けますか？',
        useLawyer: 'AI弁護士を起動',
        interpretFinish: 'AI解読完了',
        paySuccess: '支払い成功',
        payFail: '支払い失敗',
        payCanceled: '支払いキャンセル',
        paymentProcessing: 'チャージ処理中',
        paymentPendingTip: 'チャージ処理中です。しばらくお待ちください。支払い状況を定期的に確認し、完了次第自動更新いたします。',
        recheckPayment: '再確認',
        checkPaymentFailed: '支払い状況の確認に失敗しました。しばらくしてから再試行してください',
    },
    hubblePackage: {
        title: '私のパッケージ',
        details: 'パッケージの詳細',
        remainingPages: '残りの総ページ数',
        pages: 'ページ',
        usedPages: '使用済み',
        remaining: '利用可能残数',
        total: '合計',
        expiryTime: '有効期限',
        amount: '数量',
        unitPrice: '単価',
        copy: '部',
        words: '千文字',
    },
    commonFooter: {
        record: 'ICP主体企業登録番号：浙ICP備14031930号',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'オープンプラットフォーム',
        aboutBestSign: '弊社について',
        contact: 'お問い合わせ先',
        recruitment: '採用情報',
        help: 'ヘルプセンター',
        copyright: '無断転載禁止',
        company: 'ベストサイン・ジャパン株式会社',
        ssqLogo: 'ベストサイン ボトムバーロゴ',
        provideTip: '電子契約サービスは',
        ssq: 'ベストサインにより',
        provide: '提供',
        signHotline: '契約サービスホットライン',
        langSwitch: '言語',
    },
    ...components,
};
const workspace = {
    workspaceIndex: {
        title: 'ワークスペース',
        package: 'パッケージ使用量',
        agreement: '契約管理',
        review: 'レビュー管理',
        term: '用語管理',
    },
    agreement: {
        title: '契約管理',
        exportList: '契約リストをエクスポート',
        exportAllChecked: 'Excel（すべてのフィールド、選択された契約）',
        exportCurrentChecked: 'Excel（現在のフィールド、選択された契約）',
        exportAllMatched: 'Excel（すべてのフィールド、条件に一致）',
        exportCurrentMatched: 'Excel（現在のフィールド、条件に一致）',
        add: '契約を追加',
        upload: '契約をアップロード',
        operation: '操作',
        download: 'ダウンロード',
        details: '詳細',
        delete: '削除',
        relatedTaskStatus: '関連する抽出タスクの状態',
        confirmDelete: '現在の契約を削除しますか？',
        prompt: 'プロンプト',
        booleanYes: 'はい',
        booleanNo: 'いいえ',
        defaultExportName: 'export.xlsx',
        taskNotStarted: '抽出タスクは開始されていません',
        taskStarted: '抽出タスクが開始されました（コンテンツ検索中）',
        contentSearchCompleted: 'コンテンツ検索が完了しました（結果をフォーマット中）',
        resultFormattingCompleted: '結果のフォーマットが完了しました（結果を校正中）',
        resultVerificationCompleted: '結果の校正が完了しました',
    },
    filter: {
        filter: 'フィルター',
        refreshExtraction: '抽出をリフレッシュ',
        extractTerms: '用語定義を抽出',
        refreshList: 'リストをリフレッシュ',
        currentCondition: '現在の条件で表示されている契約。',
        when: '時',
        selectCondition: '条件を選択してください',
        enterCondition: '条件を入力してください',
        yes: 'はい',
        no: 'いいえ',
        addCondition: '条件を追加',
        reset: 'リセット',
        confirm: '確認',
        and: 'かつ',
        or: 'または',
        equals: '等しい',
        notEquals: '等しくない',
        contains: '含む',
        notContains: '含まない',
        greaterThan: 'より大きい',
        greaterThanOrEquals: '以上',
        lessThan: '未満',
        lessThanOrEquals: '以下',
        emptyCondition: 'フィルター条件は空にできません',
    },
    fieldConfig: {
        button: 'フィールド設定',
        header: '表示するフィールド',
        submit: '完了',
        cancel: 'キャンセル',
    },
    agreementDetail: {
        detail: '契約詳細',
        add: '契約を追加',
        id: '契約番号',
        file: '契約ファイル',
        download: '契約をダウンロード',
        replaceFile: '契約ファイルの置換',
        uploadFile: '契約ファイルをアップロード',
        relatedExtractionStatus: '関連する抽出タスクのステータス',
        dataSource: 'データソース',
        yes: 'はい',
        no: 'いいえ',
        select: '選択してください',
        input: '入力してください',
        save: '保存',
        cancel: 'キャンセル',
        page: '第 {page} ページ',
        addDataSource: 'データソースを追加',
        pageNo: '第',
        pageSuffix: 'ページ',
        submit: '送信',
        inputDataSource: 'データソース内容を入力してください',
        pageFormatError: 'ページ番号はカンマ区切りの数字のみをサポートします',
        confirmDelete: '現在のデータソースを削除しますか？',
        tips: 'ヒント',
        uploadSuccess: 'アップロード成功',
    },
    termManagement: {
        title: '用語管理',
        batchDelete: '一括削除',
        import: '用語のインポート',
        export: '用語のエクスポート',
        add: '+ 用語を追加',
        name: '用語名',
        definition: '用語の定義',
        formatRequirement: '抽出形式の要件',
        dataFormat: 'データ形式',
        operation: '操作',
        edit: '編集',
        delete: '削除',
        detail: '用語詳細',
        addTitle: '用語を追加',
        namePlaceholder: '専門用語を記入してください',
        definitionPlaceholder: '用語の定義を記入してください',
        formatRequirementPlaceholder: '用語の抽出形式の要件を記入してください',
        dataFormatPlaceholder: '期待する抽出用語形式',
        cancel: 'キャンセル',
        confirmEdit: '変更を確認',
        importTitle: '用語のインポート',
        uploadTemplate: '用語テンプレートファイルをアップロード',
        downloadTemplate: '用語テンプレートファイルをダウンロード',
        extractType: {
            text: 'テキスト',
            longText: '長いテキスト',
            date: '日付',
            number: '数値',
            boolean: 'はい/いいえ',
        },
        importSuccess: 'インポート成功',
        deleteConfirm: '現在の用語を削除しますか？',
        prompt: 'プロンプト',
        nameEmptyError: '用語名は空にできません',
    },
};
export default {
    ...lang,
    ...workspace,
    lang: 'ja',
};
