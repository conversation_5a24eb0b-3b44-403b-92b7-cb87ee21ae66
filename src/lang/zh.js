
const components = {
    authorize: {
        name: '授权',
        authorizeFile: '文件授权',
        authorizeTip: '您好，使用该功能需要授权上上签解析合同文件内容。',
        authorizeAgree: '《合同授权协议》',
    },
    countDown: {
        resendCode: '重新获取',
        getVerifyCode: '获取验证码',
    },
    guide: {
        name: '功能指引',
        description1: 'Hubble，一种全新的工作体验。',
        description2: '你好，我是哈勃，你的签约智能助手',
        description3: '一款由上上签发布的具有大模型能力的人工智能应用，提供更高效、便捷的签约互动能力。',
        description4: '我们准备了必要的使用介绍来帮助快速上手，',
        description5: '可以通过下方按钮轻松探索特色功能。',
        startHubble: '即刻开启 Hubble 之旅',
        newFileChat: '新建文档对话',
        inputContent: '输入对话内容',
        underlineQuote: '划词引用',
        outputLanguage: '输出内容语言',
        suggestQuestions: '建议问题',
    },
    header: {
        accountConfig: '账号设置',
        share: '分享',
        myVersion: '我的版本',
        shareHubble: '分享 Hubble 智能文档',
        inviteToHubble: {
            1: '邀请您参与Hubble智能文档',
            2: '合同名称：',
            3: '点击链接直接进入：',
            4: '查看密码：',
            5: '复制该信息，分享给其他人即可',
        },
        openClose: '开启/关闭分享链接，将决定其他人是否可以继续查看文档',
        inviteFriend: '输入好友手机号，邀请体验：',
        inputFriendNumber: '请输入好友手机号',
        sendInvite: '发送邀请',

        packageDetail: '套餐详情',
        packageVersion: '套餐版本：',
        currentPackage: '当前套餐：',
        validDuration: '（生效及截止日期）',
        hasUsedNum: '已用{num}份',
        hasUsedTimes: '已用{time}次',
        totalNums: '共{num}份文档',
        totalTimes: '共{times}次对话',
        buyedPackage: '已购套餐：',
        buyedContent: '（{num}期，套餐容量同上）',
        invitation: '邀请好友',
    },
    helperFloat: {
        advice: '咨询建议',
        hide: '隐藏',
        suggest: '提建议',
        onlineService: '在线客服',
        minimize: '最小化',
        enlarge: '放大',
        zoomOut: '缩小',
        person: '个人',
        ent: '企业',
        checkMoreVersion: '查看更多版本',
        submitSuccess: '上报成功',
        reportLog: '上报日志',
    },
    package: {
        myVersion: '我的版本',
        tryVersion: '体验版',
        free: 'Free / 免费',
        packageDetail: '套餐包含内容为：',
        numFiles: '份文档',
        timesChat: '次对话',
        pagePerFile: '300页/文档',
        sizePerFile: '20MB/文档',
        hasOpen: '已开通',
        validTo: '有效期至',
        openNow: '立即开通',
        personVersion: '个人版',
        costPerMonth: '${price} / 月',
        continueBuy: '续充套餐',
        groupVersion: '团队版',
        connectUs: '联系我们',
        connectTip: '可以联系我们的专业人员，我们会根据您的情况提供专属方案。',
        hasRead: '我已阅读并同意',
        agreenName: '《文档授权协议》',
        readAgreeTip: '请先阅读并同意《文档授权协议》',
    },
    quickPay: {
        pleaseScanPay: '请扫码支付',
        openFun: '开通功能：',
        payAmount: '支付金额：',
        unit: '元',
        payTip: '1.请扫码完成支付，完成支付后可在个人中心-我的订单中申请发票。',
        aliPay: '支付宝支付',
        weixinPay: '微信支付',
        payOnline: 'Payment online',
        poweredBy: 'Powered by',
        pay: 'pay',
    },
};
const lang = {
    common: {
        tip: '提示',
        confirm: '确定',
        confirmWS: '确 认',
        cancel: '取消',
        exit: '退出',
        send: '发送',
        rename: '重命名',
        delete: '删除',
        copyInfo: '复制信息',
        newName: '新名称',
    },
    staticOpt: {
        goHome: '返回首页',
        downloadApp: '下载上上签APP',
    },
    tips: {
        noData: '没有数据',
        deleteSuccess: '删除成功',
        copySuccess: '复制成功',
        openSuccess: '开通成功',
        openFailed: '开通失败',
        renameSuccess: '重命名成功',
        packageNotEnough: '套餐余额不足',
        uploadFailed: '上传失败',
        noMore: '没有更多了',

        deleteChatTip: '此操作将永久删除该话题, 是否继续?',
        pleaseOpenVersion: '请先开通需要的版本',
        pdfSupportOnly: '目前仅支持PDF文档',
        inputPhone: '请输入手机号',
        inputVerifyCode: '请输入验证码',
        lackAccount: '请填写账号后再获取',
        pleaseCheckAuto: '请点击按钮进行智能验证',
        notAccessPage: '无法访问此页面',
        page404: '没有找到您访问的页面（404）',
        systemOcupied: '系统繁忙，请稍后再试。',
        pageExpired: '页面已过期，请刷新重试',
        networkError: '当前网络不可用，请检查你的网络设置',
    },
    login: {
        welcome: '欢迎使用Hubble',
        welcomeTip: '智能签约助手',
        name: '登录',
        loginOrRegister: '登录/注册',
        hasReadAndAgree: '我已阅读并同意',
        and: '和',
        bestsignAgreement: '上上签服务协议',
        digitalCertificateAgreement: '数字证书使用协议',
        privacyPolicy: '隐私政策',
        autoRegisterForNewPhone: '未注册手机号验证后会自动创建账号',
        sendSuc: '发送成功',
    },
    upload: {
        name: '上传文档',
        selectFile: '选择文档',
        dragToUpload: '将文档拖拽至此上传',
        pdfOnly: '目前仅支持PDF文档',
        releaseMouseToUpload: '释放鼠标完成上传',
    },
    share: {
        inviteToUse: '邀请您参与Hubble智能文档',
        fileName: '文档名称：',
        inputShareCodeToView: '填写分享码查看文档：',
        inputShareCodeTip: '请输入6位数字分享码',
    },
    chat: {
        switchOutputLanguage: '输出语言切换',
        chinese: '中文语言',
        english: '英文语言',
        selectAndClickAsk: '选中文字后点击提问',
        downloadFile: '文档下载',
        sourceFile: '源文件',
        sourceFileWithChat: '源文件 (携带对话)',
        fileAnalyzing: '文档解析中',
        continuousChat: '连续对话模式：',
        quoteContent: '引用内容：',
        suggestionsQuestions: '建议问题',
        inputQuestionTip: '请输入您的问题',

        selectToQuestTip: '在下方输入或在合同上选取后点击“Hubble”，进行提问',
        question: '问题：',
        explainTip: '~ 以上内容为AI生成，不代表上上签立场，仅供您参考，请勿删除或修改本标记',
        relatedContent: '相关内容：',
        deleteConversation: '删除对话',
        openContinuousModel: '开启连续对话',
        confirmToDelete: '确认删除该对话吗？',
    },
    workspace: {
        create: '已创建',
        reviewing: '审查中',
        completed: '已完成',
        noData: '暂无数据',
        introduce: '{keyword}释义',
        termsDetail: '术语详情',
        extractFormat: '提取格式',
        optional: '选填',
        required: '必填',
        operate: '操作',
        detail: '详情',
        delete: '删除',
        agreement: {
            uploadError: '只能上传PDF、DOC、DOCX文件!',
            extractionRequest: '已发起提取请求，请稍后至术语列表中查看提取结果',
            upload: '文件上传',
            define: '术语定义',
            extract: '协议抽取',
            drag: '将文件拖拽到此处，或',
            add: '点击添加',
            format: '支持doc、docx、pdf格式',
            fileName: '文件名称',
            status: '状态',
            completed: '上传完成',
            failed: '上传失败',
            size: '大小',
            terms: '术语',
            success: '文件抽取完成，共{total}个',
            ongoing: '文件正在抽取中...共{total}个',
            tips: '跳过该界面不影响抽取结果',
            others: '继续上传',
            result: '跳转至抽取结果下载页面',
            curProgress: '当前进度: ',
            refresh: '刷新',
            details: '已加载{successNum}个，共{length}个',
            start: '开始抽取',
            more: '添加文件',
            skip: '跳过抽取，完成上传。',
            tiqu: '开始提取',
            chouqu: '开始抽取',
        },
        review: {
            distribution: '分发审查',
            Incomplete: '未结束',
            createReview: '创建审查',
            manageReview: '审查管理',
            reviewDetail: '审查详情',
            reviewId: '审查编号',
            reviewStatus: '审查状态',
            reviewName: '审查名称',
            reviewStartTime: '审查发起时间',
            reviewCompleteTime: '审查结束时间',
            reviewDesc: '版本：版本{reviewVersion}  |  审阅编号：{reviewId}',
            distribute: '发起审查',
            drag: '拖动待审查的协议到当前区域',
            content: '待审阅的内容',
            current: '待分发记录',
            history: '历史记录',
            page: '第{page}页：',
            users: '需要审阅的用户',
            message: '留言',
            modify: '修改',
            placeholder: '多个用户请使用分号";"进行分隔',
            submit: '确定',
            reupload: '重新上传协议审查',
            finish: '结束审查',
            reviewSummary: '审查概要',
            initiator: '审查发起人',
            versionSummary: '版本概要',
            version: '版本',
            versionOrder: '第{version}版',
            curReviewStatus: '当前版本审查状态',
            curReviewVersion: '当前版本',
            curReviewPopulation: '当前版本审查人数',
            curReviewStartTime: '当前版本审查发起时间',
            curReviewInitiator: '当前版本审查发起人',
            checkComments: '聚合查看修订意见',
            overview: '审查结果速览',
            reviewer: '审查人',
            reviewResult: '审查结果',
            replyTime: '回复时间',
            agreement: '审查的协议',
            files: '相关协议',
            fileName: '协议名称',
            numberOfModificationSuggestions: '修改意见数',
            uploadTime: '上传时间',
            download: '下载',
            dispatch: '分发',
            recent: '最新审查时间：',
            replyContent: '审查回复内容',
            advice: '协议修改意见',
            noIdea: '暂无修改意见',
            origin: '原文内容：',
            revised: '修改后内容：',
            suggestion: '修改意见：',
            revisionFiles: '修订协议',
            staffReplyAggregation: '聚合修订信息',
            staffReply: '{name}的审查信息',
            tips: '提示',
            tipsContent: '结束后此次审查将不再支持分发及后续操作，是否继续',
            confirm: '确定',
            cancel: '取消',
            successMessage: '已结束',
            PASS: '通过',
            NOT_PASS: '不通过',
            uploadErrorMessage: '目前只支持上传docx格式的文件',
            successInitiated: '已发起审查',
        },
        contentTracing: {
            title: '内容溯源',
            fieldContent: '字段内容',
            originalResult: '原始结果',
            contentSource: '内容源自',
            page: '第',
        },
    },
    pdf: {
        previewFail: '文件预览失败',
        pager: '第{x}页，共{y}页',
        parseFailed: '解析pdf文件失败，请点击“确定”重试',
        confirm: '确定',
    },
    contractCompare: {
        reUpload: '重新上传',
        title: '合同比对',
        packagePurchase: '套餐购买',
        packagePurchaseTitle: '【{title}功能】套餐购买',
        payOnce: '特惠限购一次',
        myPackage: '我的套餐',
        packageDetail: '套餐详情',
        per: '次',
        packageContent: '套餐包含内容为：',
        num: '{type}次数',
        limitTime: '有效期',
        month: '月',
        payNow: '立即购买',
        contactUs: '联系我们 | 扫码联系上上签专业顾问了解',
        compareInfo1: '使用说明：',
        compareInfo2: '{index}、购买的{type}可用{per}数，对应企业所有成员均可使用，如你仅需个人使用，可在右上角登录主体切换到个人账号；',
        compareInfo3: '{index}、按上传的合同{per}数统计用量',
        codePay: '请用扫码支付',
        aliPay: '支付宝支付',
        wxPay: '微信支付',
        payIno: '开通功能 | 购买对象 | 支付金额',
        finishPay: '完成支付',
        paySuccess: '购买成功',
        originFile: '原始合同文件',
        compareFile: '比对合同文件',
        documentSelect: '选择文件',
        comparisonResult: '比对结果',
        history: '历史记录',
        currentHistory: '文档记录',
        noData: '暂无数据',
        differences: '{num}处差异',
        historyLog: '{num}条记录',
        uploadLimit: '将要比对的文件拖拽至此上传 | 目前支持PDF（含PDF扫描件）、Word文件',
        dragInfo: '释放鼠标完成上传',
        uploadError: '文件格式不支持',
        pageNum: '第{page}页',
        difference: '差异{num}',
        download: '下载比对结果',
        comparing: '合同比对中...',
        tip: '提示',
        confirm: '确定',
        toBuy: '去购买',
        translate: '合同翻译',
        doCompare: '比对',
        doTranslate: '翻译',
        review: '合同审查',
        doReview: '审查',
        reviewUploadFile: '将被审查的文件拖拽至此上传',
        reviewUpload: '将审查依据拖拽至此上传 | 如：《经销商管理办法》《公司采购制度文件》等用于审查合同的公司规章制度性文件 | 目前仅支持PDF、Word文件',
        reviewOriginFile: '被审查合同',
        reviewTargetFile: '审查依据',
        reviewResult: '审查结果',
        uploadReviewFile: '上传审查依据文件',
        risk: '风险点{num}',
        risks: '{num}处风险点',
        startReview: '开始审查',
        reviewing: '合同审查中...',
        noRisk: '审查已完成，未发现风险',
        allowUpload: '可上传《公司采购管理办法》等可指导合同审查的规章制度条例，或上传公司红线规定、部门业务指导等， | 如：甲方需在合同签订后的5日内完成付款。',
        notAllowUpload: '不要以模糊语句或者原则性描述作为审查依据， | 如：所有合同条款不得违法相关法律法规要求。',
        resumeReview: '继续下一份',
        close: '关闭',
        extract: '合同抽取',
        extractTitle: '期望抽取的关键词',
        selectKeyword: '请从下方“关键词”中勾选',
        keyword: '关键词',
        addKeyword: '添加{keyword}',
        introduce: '{keyword}释义',
        startExtract: '开始抽取',
        extractTargetFile: '被抽取合同',
        extractKeyWord: '抽取关键词',
        extracting: '合同抽取中',
        extractResult: '抽取结果',
        extractUploadFile: '将被抽取的文件拖拽至此上传',
        needExtractKeyword: '请选择期望抽取的关键词',
        summary: '合同摘要',
        keySummary: '关键词内容摘要',
        deleteKeywordConfirm: '确定要删除该关键词吗？',
        keywordPosition: '关键词相关位置',
        riskJudgement: '风险判断',
        judgeTargetContract: '被判断合同',
        interpretTargetContract: '被解读合同',
        startJudge: '开始风险判断',
        startInterpret: '开始解读',
        uploadText: '请上传需要进行风险判断的文件',
        interpretText: '请上传需要进行解读的文件',
        startTips: '现在我们可以开始判断风险了',
        interpretTips: '现在我们可以开始进行解读了',
        infoExtract: '协议提取',
    },
    judgeRisk: {
        title: '风险判断',
        deepInference: '深度推理',
        aiInterpret: 'AI解读',
    },
    keyInfoExtract: {
        operate: '要素提取',
    },
    agent: {
        extractTitle: '信息提取',
        riskTitle: 'AI律师',
        feedback: '问卷反馈',
        toMini: '去小程序查看',
        otherContract: '让我看看其他合同的隐藏风险?',
        others: '其他',
        submit: '发送',
        autoExtract: '自动进行下一步提取直到提取结束',
        autoRisk: '自动进行下一步分析直到分析结束',
        aiGenerated: '以上内容为AI生成，不代表上上签立场，请勿删除或修改本标记。',
        chooseRisk: '请选择需要进行分析的文件',
        chooseExtract: '请选择需要进行提取的文件',
        analyzing: '内容分析中',
        advice: '修改建议生成中',
        options: '选项生成中',
        inputTips: '请输入确切内容',
        chargeTip: '余额不足，请充值',
        original: '原文',
        revision: '修改建议',
        diff: '对比',
        locate: '获取原文定位中',
        custom: '请输入自定义审查规则',
        content: '原文位置',
        satisfy: '对分析结果满意，继续下一项分析',
        dissatisfy: '对分析结果不满意，重新进行分析',
        selectFunc: '请选择你期望使用的功能。',
        deepInference: 'AI律师（深度推理）',
        deepThinking: '深度思考中',
        deepThoughtCompleted: '已深度思考',
        reJudge: '重新判断',
        confirm: '确认',
        tipsContent: '重新判断将会扣除份数，是否继续?',
        useLawyer: '使用AI律师',
        interpretFinish: 'AI解读已完成',
        paySuccess: '支付成功',
        payFail: '支付失败',
        payCanceled: '支付已取消',
        paymentProcessing: '充值处理中',
        paymentPendingTip: '您的充值正在处理中，请耐心等待。我们会定期检查支付状态，一旦完成会自动更新。',
        recheckPayment: '重新查询',
        checkPaymentFailed: '查询支付状态失败，请稍后重试',
    },
    hubblePackage: {
        remaining: '剩余',
        usedPages: '已使用',
        total: '总计',
        amount: '数量',
        unitPrice: '单价',
        pages: '页',
        words: '字',
        copy: '份',
    },
    entAuth: {
        validity: '有效期',
    },
    commonFooter: {
        aboutBestSign: '关于上上签',
        contact: '联系我们',
        copyright: '版权所有',
        company: '上上签电子签约云平台',
    },
    ...components,
};
const workspace = {
    workspaceIndex: {
        title: '工作空间',
        package: '套餐用量',
        agreement: '协议管理',
        review: '审查管理',
        term: '术语管理',
    },
    agreement: {
        title: '协议管理',
        exportList: '导出协议列表',
        exportAllChecked: 'Excel(全部字段，勾选协议)',
        exportCurrentChecked: 'Excel(当前字段，勾选协议)',
        exportAllMatched: 'Excel(全部字段，符合条件)',
        exportCurrentMatched: 'Excel(当前字段，符合条件)',
        add: '添加协议',
        upload: '上传协议',
        operation: '操作',
        download: '下载协议',
        details: '详情',
        delete: '删除',
        relatedTaskStatus: '关联提取任务状态',
        confirmDelete: '是否确认删除当前协议',
        prompt: '提示',
        booleanYes: '是',
        booleanNo: '否',
        defaultExportName: 'export.xlsx',
        taskNotStarted: '抽取任务未开始',
        taskStarted: '抽取任务已开始 (内容检索中)',
        contentSearchCompleted: '内容检索已完成 (结果格式化中)',
        resultFormattingCompleted: '结果格式化已完成 (结果校对中)',
        resultVerificationCompleted: '结果校对已完成',
    },
    filter: {
        filter: '过滤',
        refreshExtraction: '重新提取',
        extractTerms: '术语定义提取',
        refreshList: '刷新列表',
        currentCondition: '当前条件下，展示的协议。',
        when: '当',
        selectCondition: '请选择条件',
        enterCondition: '请输入条件',
        yes: '是',
        no: '否',
        addCondition: '添加条件',
        reset: '重置',
        confirm: '确定',
        and: '且',
        or: '或',
        equals: '等于',
        notEquals: '不等于',
        contains: '包含',
        notContains: '不包含',
        greaterThan: '大于',
        greaterThanOrEquals: '大于等于',
        lessThan: '小于',
        lessThanOrEquals: '小于等于',
        emptyCondition: '过滤条件不能为空',
    },
    fieldConfig: {
        button: '字段配置',
        header: '期望展示的字段',
        submit: '完成',
        cancel: '取消',
    },
    agreementDetail: {
        detail: '协议详情',
        add: '添加协议',
        id: '协议编号',
        file: '协议文件',
        download: '协议下载',
        replaceFile: '协议文件替换',
        uploadFile: '协议文件上传',
        relatedExtractionStatus: '关联提取任务状态',
        dataSource: '数据来源',
        yes: '是',
        no: '否',
        select: '请选择',
        input: '请输入',
        save: '保存',
        cancel: '取消',
        page: '第 {page} 页',
        addDataSource: '添加数据来源',
        pageNo: '第',
        pageSuffix: '页',
        submit: '提交',
        inputDataSource: '请输入数据来源内容',
        pageFormatError: '页码只支持英文逗号隔开的数字或者纯数字',
        confirmDelete: '是否确认删除当前数据来源',
        tips: '提示',
        uploadSuccess: '上传成功',
    },
    termManagement: {
        title: '术语管理',
        batchDelete: '批量删除',
        import: '术语导入',
        export: '术语导出',
        add: '+ 添加术语',
        name: '术语名称',
        definition: '术语释义',
        formatRequirement: '提取格式要求',
        dataFormat: '数据格式',
        operation: '操作',
        edit: '修改',
        delete: '删除',
        detail: '术语详情',
        addTitle: '添加术语',
        namePlaceholder: '填写你的专业术语',
        definitionPlaceholder: '填写术语的释义',
        formatRequirementPlaceholder: '填写术语的提取格式要求',
        dataFormatPlaceholder: '期望提取的术语格式要求',
        cancel: '取消',
        confirmEdit: '确认修改',
        importTitle: '术语导入',
        uploadTemplate: '上传术语模板文件',
        downloadTemplate: '下载术语模板文件',
        extractType: {
            text: '文本',
            longText: '长文本',
            date: '日期',
            number: '数值',
            boolean: '是/否',
        },
        importSuccess: '导入成功',
        deleteConfirm: '是否确认删除当前术语',
        prompt: '提示',
        nameEmptyError: '术语名称不能为空',
    },
};
export default {
    ...lang,
    ...workspace,
    lang: 'zh',
};
